import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Chip,
  CircularProgress,
  Alert,
  Paper,
  Grid,
  Button,
  Snackbar
} from '@mui/material';
import { RichTreeView } from '@mui/x-tree-view/RichTreeView';
import {
  Save as SaveIcon
} from '@mui/icons-material';
import { useVariableTree } from '../../lib/hooks/useVariableTree';
import { useVariableTreeState } from '../../lib/hooks/useVariableTreeState';
import { VariableStatusBadge } from './VariableStatusBadge';
import { VariableInputRenderer } from './VariableInputRenderer';
import { VariableFocusIndicator } from './VariableFocusIndicator';
import { CustomTreeItem } from './CustomTreeItem';

interface VariableTreeViewProps {
  templateId: number;
  onVariableSelect?: (variableName: string) => void;
}

interface TreeItemData {
  id: string;
  label: string;
  type: 'category' | 'dataset' | 'global';
  variables?: any[];
  children?: TreeItemData[];
  nodeId?: number;
  datasetId?: number;
}

// Build tree structure from the new hierarchical API response with Global root node
const buildTreeFromVariableData = (variableTreeNodes: any[], templateVariables: any[], templateName?: string): TreeItemData[] => {
  const convertNode = (node: any): TreeItemData => {
    const treeItem: TreeItemData = {
      id: `category-${node.id}`,
      label: node.description ? `${node.name} (${node.description})` : node.name,
      type: 'category',
      variables: node.variables || [],
      children: [],
      nodeId: node.id
    };

    // Add datasets as children if they exist
    if (node.datasets && Array.isArray(node.datasets)) {
      node.datasets.forEach((dataset: any) => {
        const datasetItem: TreeItemData = {
          id: `dataset-${dataset.id}`,
          label: dataset.description ? `${dataset.name} (${dataset.description})` : dataset.name,
          type: 'dataset',
          variables: dataset.variables || [],
          nodeId: node.id,
          datasetId: dataset.id
        };
        treeItem.children!.push(datasetItem);
      });
    }

    // Add child categories recursively if they exist
    if (node.children && Array.isArray(node.children)) {
      node.children.forEach((childNode: any) => {
        const childItem = convertNode(childNode);
        treeItem.children!.push(childItem);
      });
    }

    return treeItem;
  };

  // Create Global root node
  const globalRoot: TreeItemData = {
    id: 'global-root',
    label: templateName ? `Global (${templateName})` : 'Global',
    type: 'global',
    variables: templateVariables || [],
    children: variableTreeNodes.map(convertNode)
  };

  return [globalRoot];
};

// Helper function to sort tree items recursively
const sortTreeItems = (items: TreeItemData[]): TreeItemData[] => {
  return items
    .sort((a, b) => a.label.localeCompare(b.label))
    .map(item => ({
      ...item,
      children: item.children ? sortTreeItems(item.children) : undefined
    }));
};

export const VariableTreeView: React.FC<VariableTreeViewProps> = ({
  templateId,
  onVariableSelect
}) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [selectedItems, setSelectedItems] = useState<string | null>(null);
  const [selectedNode, setSelectedNode] = useState<TreeItemData | null>(null);
  const [treeItems, setTreeItems] = useState<TreeItemData[]>([]);
  const [treeLoading, setTreeLoading] = useState(false);
  const [treeError, setTreeError] = useState<string | null>(null);
  const [showInputs, setShowInputs] = useState(true);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [showErrorSnackbar, setShowErrorSnackbar] = useState(false);

  // Right-panel scroll management: per-level scroll positions
  const variableListContainerRef = useRef<HTMLDivElement | null>(null);
  const scrollPositionsRef = useRef<Record<string, number>>({});
  const currentLevelIdRef = useRef<string | null>(null);


  const getLevelKey = useCallback((node: TreeItemData | null, explicitId?: string | null) => {
    if (explicitId) return explicitId;
    if (!node) return 'none';
    return node.id; // ids are already like 'global-root' | 'category-<id>' | 'dataset-<id>'
  }, []);

  const {
    data,
    loading,
    error,
    refetch,
    getVariablesByName,
    getVariableState,
    getVariableStateForContext,
    focusVariable,
    unfocusVariable,
    isVariableFocused,
    focusedVariable,
    getInheritedValueForContext
  } = useVariableTree({ templateId });

  // State management for variable changes
  const {
    hasChanges,
    updateVariable,
    resetAllChanges,
    saveChanges,
    resetToInherited,
    isSaving,
    saveError,
    isResetting,
    resetError,
    getVariableValue,
    isVariableChanged,
    isVariableResetPending
  } = useVariableTreeState({
    templateId,
    onSaveSuccess: () => {
      console.log('Variables saved successfully');
      setSaveSuccess(true);
      // Refetch the variable tree data to reflect changes
      refetch();
    },
    onSaveError: (error) => {
      console.error('Failed to save variables:', error);
      setShowErrorSnackbar(true);
    }
  });
  console.log('Variable tree data:', data);
  // Build tree from variable tree data when it's available

  useEffect(() => {
    if (data?.tree) {
      setTreeLoading(true);
      try {
        // Build tree directly from variable tree data with Global root
        const hierarchicalTree = buildTreeFromVariableData(
          data.tree,
          data.template_variables || [],
          'Template' // TODO: Get actual template name
        );
        console.log('Built hierarchical tree:', hierarchicalTree);
        // Set current level id to default Global root for correct initial scroll handling
        currentLevelIdRef.current = 'global-root';

        setTreeItems(sortTreeItems(hierarchicalTree));

        // Auto-expand Global root and first level categories
        const expandIds = ['global-root'];
        if (hierarchicalTree[0]?.children) {
          expandIds.push(...hierarchicalTree[0].children.map(item => item.id));
        }
        console.log('Auto-expanding items:', expandIds);
        setExpandedItems(expandIds);

        // Default select Global root
        setSelectedItems('global-root');
        setSelectedNode(hierarchicalTree[0]);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        setTreeError(errorMessage);
        console.error('Error building tree:', err);
      } finally {
        setTreeLoading(false);
        console.log('Tree items:', treeItems);
      }
    }
  }, [data]);

  // Helper function to get variables defined directly in a specific node (not inherited)
  const getVariablesDefinedInNode = React.useCallback((nodeId?: number, datasetId?: number): string[] => {
    if (!data) return [];

    const variableNames = new Set<string>();

    // Find the specific node
    const findNode = (nodes: any[], targetNodeId: number): any => {
      for (const node of nodes) {
        if (node.id === targetNodeId) {
          return node;
        }
        if (node.children) {
          const found = findNode(node.children, targetNodeId);
          if (found) return found;
        }
      }
      return null;
    };

    if (nodeId) {
      const targetNode = findNode(data.tree, nodeId);
      if (targetNode) {
        // Add variables defined directly in this node
        if (targetNode.variables) {
          targetNode.variables.forEach((v: any) => variableNames.add(v.name));
        }

        // If we're looking at a specific dataset, add its variables too
        if (datasetId && targetNode.datasets) {
          const targetDataset = targetNode.datasets.find((d: any) => d.id === datasetId);
          if (targetDataset && targetDataset.variables) {
            targetDataset.variables.forEach((v: any) => variableNames.add(v.name));
          }
        }
      }
    }

    return Array.from(variableNames);
  }, [data]);

  // Helper function to collect all unique variable names for a context (including inherited)
  const getAllVariablesForContext = React.useCallback((nodeId?: number, datasetId?: number) => {
    if (!data) return [];

    const allVariableNames = new Set<string>();

    // Add template variables
    data.template_variables.forEach((v: any) => allVariableNames.add(v.name));

    // Find the path to the current context
    const findNodePath = (nodes: any[], targetNodeId: number): any[] => {
      for (const node of nodes) {
        if (node.id === targetNodeId) {
          return [node];
        }
        if (node.children) {
          const childPath = findNodePath(node.children, targetNodeId);
          if (childPath.length > 0) {
            return [node, ...childPath];
          }
        }
      }
      return [];
    };

    if (nodeId) {
      const nodePath = findNodePath(data.tree, nodeId);

      // Add variables from all nodes in the path (inheritance chain)
      nodePath.forEach(node => {
        if (node.variables) {
          node.variables.forEach((v: any) => allVariableNames.add(v.name));
        }
      });

      // If we're looking at a specific dataset, add its variables too
      if (datasetId && nodePath.length > 0) {
        const parentNode = nodePath[nodePath.length - 1];
        const targetDataset = parentNode.datasets?.find((d: any) => d.id === datasetId);
        if (targetDataset && targetDataset.variables) {
          targetDataset.variables.forEach((v: any) => allVariableNames.add(v.name));
        }
      }
    }

    return Array.from(allVariableNames).sort();
  }, [data]);

  // Navigate to the defining level of a variable
  const navigateToDefiningLevel = useCallback((variableName: string) => {
    if (!data) return;

    // Find the variable's defining level
    const allVariables = getVariablesByName(variableName);
    const activeVariable = allVariables.find((v: any) => v.is_active);

    if (!activeVariable) return;

    // Determine target node based on source level
    let targetNodeId: string;
    let pathToExpand: string[] = [];

    if (activeVariable.source_level === 'Template') {
      // Navigate to Global root
      targetNodeId = 'global-root';
      pathToExpand = ['global-root'];
    } else if (activeVariable.source_level === 'Category') {
      // Find the category node that defines this variable
      const findDefiningCategory = (nodes: any[]): any => {
        for (const node of nodes) {
          if (node.variables?.some((v: any) => v.name === variableName && v.is_active)) {
            return node;
          }
          if (node.children) {
            const found = findDefiningCategory(node.children);
            if (found) return found;
          }
        }
        return null;
      };

      const definingCategory = findDefiningCategory(data.tree || []);
      if (definingCategory) {
        targetNodeId = `category-${definingCategory.id}`;
        pathToExpand = ['global-root', targetNodeId];
      } else {
        return;
      }
    } else if (activeVariable.source_level === 'Dataset') {
      // Find the dataset node that defines this variable
      const findDefiningDataset = (nodes: any[]): { category: any; dataset: any } | null => {
        for (const node of nodes) {
          if (node.datasets) {
            for (const dataset of node.datasets) {
              if (dataset.variables?.some((v: any) => v.name === variableName && v.is_active)) {
                return { category: node, dataset };
              }
            }
          }
          if (node.children) {
            const result = findDefiningDataset(node.children);
            if (result) return result;
          }
        }
        return null;
      };

      const result = findDefiningDataset(data.tree || []);
      if (result) {
        targetNodeId = `dataset-${result.dataset.id}`;
        pathToExpand = ['global-root', `category-${result.category.id}`, targetNodeId];
      } else {
        return;
      }
    } else {
      return;
    }

    // Expand the path to the target node
    setExpandedItems(prev => {
      const newExpanded = new Set([...prev, ...pathToExpand]);
      return Array.from(newExpanded);
    });

    // Before switching, save current level scroll position
    const currentKey = getLevelKey(selectedNode);
    if (variableListContainerRef.current) {
      scrollPositionsRef.current[currentKey] = variableListContainerRef.current.scrollTop;
    }

    // Select the target node
    setSelectedItems(targetNodeId);

    // Find and set the selected node
    const findNodeById = (nodes: TreeItemData[], id: string): TreeItemData | null => {
      for (const node of nodes) {
        if (node.id === id) return node;
        if (node.children) {
          const found = findNodeById(node.children, id);
          if (found) return found;
        }
      }
      return null;
    };

    const targetNode = findNodeById(treeItems, targetNodeId);
    if (targetNode) {
      setSelectedNode(targetNode);
    }

    // After selection state updates and right panel renders, restore its saved scroll first
    setTimeout(() => {
      const targetKey = targetNodeId;
      const savedScroll = scrollPositionsRef.current[targetKey] ?? 0;
      if (variableListContainerRef.current) {
        variableListContainerRef.current.scrollTop = savedScroll;
      }

      // Then scroll the specific variable into view within the right panel container
      setTimeout(() => {
        const container = variableListContainerRef.current;
        const el = document.querySelector(`[data-variable-name="${variableName}"]`) as HTMLElement | null;
        if (container && el) {
          // Compute position relative to container and center it
          const containerRect = container.getBoundingClientRect();
          const elRect = el.getBoundingClientRect();
          const delta = elRect.top - containerRect.top;
          const targetTop = container.scrollTop + delta - container.clientHeight / 2 + elRect.height / 2;
          container.scrollTo({ top: Math.max(0, targetTop), behavior: 'smooth' });

          // Add highlight effect
          const originalBackground = el.style.backgroundColor;
          const originalTransition = el.style.transition;
          el.style.backgroundColor = '#fff3cd';
          el.style.transition = 'background-color 0.3s ease';
          setTimeout(() => {
            el.style.backgroundColor = originalBackground;
            el.style.transition = originalTransition;
          }, 3000);
        }
      }, 150);
    }, 150);

  }, [data, getVariablesByName, treeItems]);

  // Find selected node when selection changes
  React.useEffect(() => {
    if (selectedItems && treeItems.length > 0) {
      const findNode = (items: TreeItemData[], id: string): TreeItemData | null => {
        for (const item of items) {
          if (item.id === id) return item;
          if (item.children) {
            const found = findNode(item.children, id);
            if (found) return found;
          }
        }
        return null;
      };
      setSelectedNode(findNode(treeItems, selectedItems));
      // Restore per-level scroll position after selection updates
      setTimeout(() => {
        const node = findNode(treeItems, selectedItems);
        const key = getLevelKey(node, selectedItems);
        const saved = scrollPositionsRef.current[key] ?? 0;
        if (variableListContainerRef.current) {
          variableListContainerRef.current.scrollTop = saved;
        }
      }, 0);

    } else {
      setSelectedNode(null);
    }
  }, [selectedItems, treeItems]);

  // Handle ESC key to unfocus variable
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && focusedVariable) {
        unfocusVariable();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [focusedVariable, unfocusVariable]);

  if (loading || treeLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || treeError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Error loading data: {error || treeError}
        <Box sx={{ mt: 1 }}>
          <Chip
            label="Try again"
            onClick={refetch}
            size="small"
            variant="outlined"
          />
        </Box>
      </Alert>
    );
  }

  if (!data && treeItems.length === 0) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No data available
      </Alert>
    );
  }

  // Handle click on empty area to unfocus variable
  const handleBackgroundClick = (event: React.MouseEvent) => {
    // Only unfocus if clicking directly on the Paper background
    if (event.target === event.currentTarget && focusedVariable) {
      unfocusVariable();
    }
  };

  return (
    <Paper
      sx={{ p: 2, height: '100%' }}
      onClick={handleBackgroundClick}
    >
      {/* Header with Save Button */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Variable Tree (Template ID: {templateId})
        </Typography>

        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          {/* Save/Reset Controls */}
          {hasChanges && (
            <>
              <Button
                variant="contained"
                size="small"
                startIcon={<SaveIcon />}
                onClick={saveChanges}
                disabled={isSaving}
                color="primary"
              >
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={resetAllChanges}
                disabled={isSaving}
                color="secondary"
              >
                Reset
              </Button>
            </>
          )}

          <Button
            variant="outlined"
            size="small"
            onClick={() => setShowInputs(!showInputs)}
          >
            {showInputs ? 'STATUS BADGES' : 'INPUT COMPONENTS'}
          </Button>
        </Box>
      </Box>

      {/* Save Error */}
      {saveError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Error saving: {saveError}
        </Alert>
      )}


                  // Update current level id ref first for stable onScroll persistence
                  currentLevelIdRef.current = typeof itemId === 'string' ? itemId : String(itemId);


      {/* Two-column layout */}
      <Grid container spacing={2} sx={{ height: 'calc(100% - 200px)' }}>
        {/* Left column: Tree */}
        <Grid item xs={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom>
              Category Hierarchy
            </Typography>
            {treeItems.length > 0 ? (
              <RichTreeView
                items={treeItems}
                expandedItems={expandedItems}
                onExpandedItemsChange={(event, itemIds) => setExpandedItems(itemIds)}
                selectedItems={selectedItems}
                onSelectedItemsChange={(event, itemId) => {
                  // Save current level scroll position before switching
                  const key = getLevelKey(selectedNode);
                  if (variableListContainerRef.current) {
                    scrollPositionsRef.current[key] = variableListContainerRef.current.scrollTop;
                  }

                  // Update current level id after selection (used by onScroll)
                  currentLevelIdRef.current = typeof itemId === 'string' ? itemId : String(itemId);

                  setSelectedItems(itemId)
                }}
                slots={{
                  item: CustomTreeItem
                }}
                slotProps={{
                  item: (ownerState: any) => {
                    // Find the tree item data for this node
                    const findTreeItem = (items: TreeItemData[], id: string): TreeItemData | null => {
                      for (const item of items) {
                        if (item.id === id) return item;
                        if (item.children) {
                          const found = findTreeItem(item.children, id);
                          if (found) return found;
                        }
                      }
                      return null;
                    };

                    const treeItem = findTreeItem(treeItems, ownerState.itemId);
                    const nodeVariables = treeItem ? getVariablesDefinedInNode(treeItem.nodeId, treeItem.datasetId) : [];

                    return {
                      focusedVariable,
                      nodeVariables,
                      getVariableState: getVariableStateForContext,
                      nodeId: treeItem?.nodeId,
                      datasetId: treeItem?.datasetId
                    } as any; // Type assertion to bypass strict typing
                  }
                }}
                sx={{
                  flexGrow: 1,
                  maxWidth: '100%',
                  overflowY: 'auto',
                  bgcolor: 'background.paper',
                  borderRadius: 1,
                  p: 1
                }}
              />
            ) : (
              <Alert severity="info">
                No categories found
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Right column: Settings */}
        <Grid item xs={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Typography variant="subtitle1">
                Settings
              </Typography>
              {selectedNode && (
                <Chip
                  label={selectedNode.type === 'global' ? 'Global' :
                         selectedNode.type === 'dataset' ? 'Dataset' : 'Category'}
                  size="small"
                  color={selectedNode.type === 'global' ? 'primary' :
                         selectedNode.type === 'dataset' ? 'secondary' : 'default'}
                  variant="outlined"
                />
              )}
            </Box>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
              🟢 Active • 🟠 Overridden • 🔘 Inherited from higher level • 🔴 Not set
            </Typography>
            {selectedNode ? (
              <Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {selectedNode.type === 'dataset' ? 'Dataset' :
                   selectedNode.type === 'global' ? 'Global' : 'Category'}: {selectedNode.label}
                </Typography>
                {(() => {
                  // Get all available variables for this context (including inherited)
                  const nodeId = selectedNode.type === 'global' ? undefined : selectedNode.nodeId;
                  const datasetId = selectedNode.type === 'global' ? undefined : selectedNode.datasetId;


                  const allVariableNames = selectedNode.type === 'global' ?
                    (data?.template_variables || []).map((v: any) => v.name) :
                    getAllVariablesForContext(nodeId, datasetId);

                  return allVariableNames.length > 0 ? (
                    <Box
                      ref={variableListContainerRef}
                      onScroll={(e) => {
                        // Persist scroll under the current level id (stable across transitions)
                        const key = currentLevelIdRef.current || getLevelKey(selectedNode);
                        scrollPositionsRef.current[key] = (e.currentTarget as HTMLDivElement).scrollTop;
                      }}
                      sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: 1,
                        maxHeight: '400px',
                        overflowY: 'auto',
                        p: 1
                      }}
                    >
                      {allVariableNames
                        .map(variableName => {
                          let variable;
                          if (selectedNode.type === 'global') {
                            // For global context, get template variables directly
                            variable = (data?.template_variables || []).find((v: any) => v.name === variableName);
                          } else {
                            // For category/dataset context, use existing logic
                            const allVariables = getVariablesByName(variableName);
                            variable = allVariables.find((v: any) =>
                              (nodeId && v.source_level === 'Category') ||
                              (datasetId && v.source_level === 'Dataset') ||
                              v.source_level === 'Template'
                            );
                          }

                          // Debug logging for GNSSES variable
                          if (variableName === 'GNSSES') {
                            console.log('GNSSES variable debug:', {
                              variableName,
                              selectedVariable: variable,
                              nodeId,
                              datasetId,
                              selectedNodeType: selectedNode.type,
                              hasGui: variable?.gui?.component_id
                            });
                          }

                          const state = selectedNode.type === 'global' ?
                            getVariableState(variableName) :
                            getVariableStateForContext(variableName, nodeId, datasetId);



                          return {
                            name: variableName,
                            state,
                            variable
                          };
                        })
                        .filter(({ variable }) => {
                          // In INPUT-KOMPONENTEN mode: only show variables with GUI components
                          // In STATUS-BADGES mode: show all variables
                          return showInputs ? variable?.gui?.component_id : true;
                        })
                        .sort((a, b) => {
                          // Sort by state priority: active > overridden > defined-higher > not-set
                          const stateOrder = { 'active': 0, 'overridden': 1, 'defined-higher': 2, 'not-set': 3 };
                          const aOrder = stateOrder[a.state.primaryState] || 3;
                          const bOrder = stateOrder[b.state.primaryState] || 3;
                          if (aOrder !== bOrder) return aOrder - bOrder;
                          // If same state, sort alphabetically
                          return a.name.localeCompare(b.name);
                        })
                        .map(({ name, state, variable }, index) => {
                          if (showInputs && variable?.gui?.component_id) {
                            return (
                              <VariableInputRenderer
                                key={index}
                                variable={variable}
                                onChange={(variableName: string, newValue: any) => {
                                  // Handle different node types for variable updates
                                  if (selectedNode.type === 'global') {
                                    // Global variables are template variables (no context ID needed)
                                    updateVariable(variableName, newValue, variable);
                                  } else {
                                    // Use datasetId for datasets, nodeId for categories
                                    const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                    updateVariable(variableName, newValue, variable, contextId, selectedNode.type);
                                  }
                                }}
                                template_data={data}
                                contextInfo={{
                                  nodeId,
                                  nodeType: selectedNode.type === 'global' ? 'category' : selectedNode.type,
                                  nodeName: selectedNode.label
                                }}
                                showStatusBadge={true}
                                variableState={state}
                                currentValue={(function() {
                                  // Determine which value to display based on status and context
                                  const isResetPending = isVariableResetPending(variable.name);
                                  const primary = state.primaryState;
                                  // Helper: find the variable defined at the current context level
                                  const findContextVariable = () => {
                                    if (selectedNode.type === 'global') {
                                      return (data?.template_variables || []).find((v: any) => v.name === variable.name);
                                    }
                                    // Find the category node by nodeId
                                    const findNodeById = (nodes: any[], id: number): any | null => {
                                      for (const n of nodes) {
                                        if (n.id === id) return n;
                                        if (n.children) {
                                          const f = findNodeById(n.children, id);
                                          if (f) return f;
                                        }
                                      }
                                      return null;
                                    };
                                    const cat = nodeId ? findNodeById(data?.tree || [], nodeId) : null;
                                    if (selectedNode.type === 'category') {
                                      return cat?.variables?.find((v: any) => v.name === variable.name);
                                    } else if (selectedNode.type === 'dataset') {
                                      const ds = cat?.datasets?.find((d: any) => d.id === datasetId);
                                      return ds?.variables?.find((v: any) => v.name === variable.name);
                                    }
                                    return undefined;
                                  };
                                  const contextVar = findContextVariable();
                                  let originalDisplayValue: any;
                                  if (isResetPending || primary === 'defined-higher') {
                                    // Show inherited value from higher level
                                    originalDisplayValue = getInheritedValueForContext(variable.name, nodeId, datasetId);
                                  } else if (primary === 'active' || primary === 'overridden') {
                                    // Show the value actually set at the current level
                                    originalDisplayValue = contextVar?.value ?? contextVar?.data;
                                    // Fallback to inherited if not found for safety
                                    if (originalDisplayValue === undefined) {
                                      originalDisplayValue = getInheritedValueForContext(variable.name, nodeId, datasetId);
                                    }
                                  } else {
                                    // not-set or other: show undefined/inherited
                                    originalDisplayValue = contextVar?.value ?? contextVar?.data;
                                  }
                                  return getVariableValue(variable.name, originalDisplayValue);
                                })()}
                                pendingReset={isVariableResetPending(variable.name)}
                                pendingOverride={isVariableChanged(variable.name) && state.primaryState === 'defined-higher'}
                                focusVariable={focusVariable}
                                unfocusVariable={unfocusVariable}
                                isVariableFocused={isVariableFocused}
                                onReset={selectedNode.type === 'global' ? undefined : (variableName) => {
                                  const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                  const nodeType = selectedNode.type as 'category' | 'dataset';
                                  const inherited = getInheritedValueForContext(variableName, nodeId, datasetId);
                                  // LOCAL reset: mark pending reset and restore inherited visuals
                                  resetToInherited(variableName, contextId, nodeType, inherited);
                                }}
                                onOverride={(variableName) => {
                                  // Create an override at the current level
                                  const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                  const nodeType = selectedNode.type === 'global' ? undefined : selectedNode.type;
                                  // Get the current active value to use as initial override value
                                  const currentValue = getVariableValue(variableName, state.activeVariable?.variable?.data);
                                  updateVariable(variableName, currentValue, variable!, contextId, nodeType);
                                }}
                                onGoToDefining={navigateToDefiningLevel}
                              />
                            );
                          } else {
                            return (
                              <Box
                                key={index}
                                sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}
                                data-variable-name={name}
                              >
                                <Box
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (isVariableFocused(name)) {
                                      unfocusVariable();
                                    } else {
                                      focusVariable(name);
                                    }
                                  }}
                                  sx={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                                >
                                  <VariableStatusBadge
                                    variableName={name}
                                    primaryState={
                                      isVariableResetPending(name)
                                        ? 'defined-higher'
                                        : (isVariableChanged(name) && state.primaryState === 'defined-higher')
                                          ? 'active'
                                          : state.primaryState
                                    }
                                    secondaryState={state.secondaryState}
                                    counts={state.counts}
                                    activeVariable={state.activeVariable}
                                    overriddenBy={state.overriddenBy}
                                    showActions={true}
                                    onGoToDefining={navigateToDefiningLevel}
                                    onOverride={(variableName) => {
                                      // Create an override at the current level
                                      const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                      const nodeType = selectedNode.type === 'global' ? undefined : selectedNode.type;
                                      // Get the current active value to use as initial override value
                                      const currentValue = getVariableValue(variableName, state.activeVariable?.variable?.data);
                                      updateVariable(variableName, currentValue, variable!, contextId, nodeType);
                                    }}
                                    currentPath={selectedNode.type === 'global' ? 'Global' :
                                      selectedNode.type === 'dataset' ? `Global → ${selectedNode.label.split(' (')[0]} → ${selectedNode.label}` :
                                      `Global → ${selectedNode.label.split(' (')[0]}`}
                                  />
                                  <VariableFocusIndicator
                                    isVisible={isVariableFocused(name)}
                                    primaryState={state.primaryState}
                                  />
                                </Box>
                              </Box>
                            );
                          }
                        })}
                    </Box>
                  ) : (
                    <Alert severity="info">
                      No settings available for this {selectedNode.type === 'dataset' ? 'dataset' :
                        selectedNode.type === 'global' ? 'global context' : 'category'}
                    </Alert>
                  );
                })()}
              </Box>
            ) : (
              <Alert severity="info">
                Select a node from the tree to view and configure settings
              </Alert>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Save Success Message */}
      {saveSuccess && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Changes saved successfully
        </Alert>
      )}

      {/* Error Snackbar */}
      <Snackbar
        open={showErrorSnackbar}
        autoHideDuration={6000}
        onClose={() => setShowErrorSnackbar(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowErrorSnackbar(false)}
          severity="error"
          sx={{ width: '100%' }}
        >
          Error saving: {saveError}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default VariableTreeView;